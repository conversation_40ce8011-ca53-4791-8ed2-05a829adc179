<!-- Page-specific styles for teacher management -->
<style>
    .teacher-row {
        transition: all 0.2s ease;
    }
    .teacher-row:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* CV Generation Button Styling */
    .generate-cv-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .generate-cv-btn:hover {
        transform: scale(1.1);
        color: #059669 !important;
    }

    .generate-cv-btn:active {
        transform: scale(0.95);
    }

    /* Button loading state */
    .generate-cv-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .generate-cv-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 12px;
        height: 12px;
        margin: -6px 0 0 -6px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<!-- Live Timetable Overview -->
<div class="mb-8">
    <!-- Timetable Header -->
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white rounded-lg p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">Live Faculty Timetable</h2>
                <p class="text-principal-light">Real-time view of ongoing and upcoming classes</p>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                    <div id="current-time" class="text-lg font-bold"></div>
                    <div class="text-sm text-principal-light">Current Time</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Timetable Grid -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Today's Live Timetable</h3>
                    <p class="text-sm text-gray-600 mt-1">Real-time class schedule with current status</p>
                </div>
                <div class="flex items-center space-x-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-gray-600">Currently Active</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">Completed</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">Upcoming</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">Cancelled</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span class="text-sm text-gray-600">Free Period</span>
                        </div>
                    </div>
                    <button id="refreshTimetable" class="btn-principal-outline px-3 py-2 rounded-lg text-sm font-medium hover:bg-principal-primary hover:text-white transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10">
                            <div class="flex items-center">
                                <i class="fas fa-users mr-2"></i>
                                Class
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 1</div>
                                <div class="text-xs text-gray-400">08:00-08:40</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 2</div>
                                <div class="text-xs text-gray-400">08:45-09:25</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 3</div>
                                <div class="text-xs text-gray-400">09:30-10:10</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 4</div>
                                <div class="text-xs text-gray-400">10:15-10:55</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 5</div>
                                <div class="text-xs text-gray-400">11:00-11:40</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 6</div>
                                <div class="text-xs text-gray-400">11:45-12:25</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 7</div>
                                <div class="text-xs text-gray-400">12:30-13:10</div>
                            </div>
                        </th>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="text-center">
                                <div class="font-bold">Period 8</div>
                                <div class="text-xs text-gray-400">13:15-13:55</div>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody id="timetable-body" class="bg-white divide-y divide-gray-200">
                    <!-- Timetable content will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Teacher Management Overview -->
<div class="mb-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                    <p class="text-2xl font-bold text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Performers</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) >= 80).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Need Attention</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) < 60).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.reduce((sum, t) => sum + (t.overdue_lectures || 0), 0) %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Performance Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Teacher Performance Dashboard</h2>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Test PDF Button -->
                <button id="testSimplePDF" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-file-pdf mr-2"></i>
                    Test PDF
                </button>
                <!-- Refresh Button -->
                <button class="action-btn btn-principal px-4 py-2 rounded-lg text-sm font-medium" data-action="refresh">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="filter-performance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                    <option value="">All Performance</option>
                    <option value="excellent">Excellent (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="average">Average (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Lectures
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Completion Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Overdue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                const completionRate = teacher.completion_rate || 0;
                                const performanceLevel = completionRate >= 80 ? 'excellent' : completionRate >= 60 ? 'good' : completionRate >= 40 ? 'average' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-performance="<%= performanceLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-principal-light flex items-center justify-center">
                                                <span class="text-sm font-medium text-principal-primary">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.delivered_lectures || 0 %></span>/<%= teacher.total_lectures || 0 %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total lectures
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= completionRate >= 80 ? 'bg-green-500' : completionRate >= 60 ? 'bg-blue-500' : completionRate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(completionRate, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(completionRate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (teacher.overdue_lectures > 0) { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <%= teacher.overdue_lectures %>
                                        </span>
                                    <% } else { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Up to date
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= completionRate >= 80 ? 'bg-green-100 text-green-800' : completionRate >= 60 ? 'bg-blue-100 text-blue-800' : completionRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (completionRate >= 80) { %>
                                            <i class="fas fa-star mr-1"></i> Excellent
                                        <% } else if (completionRate >= 60) { %>
                                            <i class="fas fa-thumbs-up mr-1"></i> Good
                                        <% } else if (completionRate >= 40) { %>
                                            <i class="fas fa-minus-circle mr-1"></i> Average
                                        <% } else { %>
                                            <i class="fas fa-times-circle mr-1"></i> Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button id="viewTeacherBtn-<%= teacher.id %>"
                                                class="view-teacher-btn text-principal-primary hover:text-principal-dark p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn text-blue-600 hover:text-blue-900 p-1 rounded"
                                                data-action="send-message"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button id="generateCVBtn-<%= teacher.id %>"
                                                class="generate-cv-btn text-green-600 hover:text-green-900 p-1 rounded"
                                                data-action="generate-cv"
                                                data-teacher-id="<%= teacher.id %>"
                                                data-teacher-name="<%= teacher.name %>"
                                                data-teacher-email="<%= teacher.email %>"
                                                title="Generate CV PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Enhanced Teacher Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-[95vw] w-full max-h-[95vh] overflow-y-auto mx-2">
    <!-- Modal Header -->
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="teacherModalTitle" class="text-xl font-semibold flex items-center">
        <i class="fas fa-user-tie mr-3"></i>
        Enhanced Teacher Profile
      </h3>
      <button id="closeTeacherModalBtn" class="text-white hover:text-gray-200">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div id="teacherModalContent" class="p-6">
      <!-- Loading state -->
      <div id="modal-loading" class="flex items-center justify-center py-12">
        <i class="fas fa-spinner fa-spin text-3xl text-principal-primary mr-3"></i>
        <span class="text-lg text-gray-600">Loading teacher profile...</span>
      </div>

      <!-- Enhanced Profile Content (will be populated) -->
      <div id="enhanced-profile-content" class="hidden">
        <!-- Personal Information Section - Full Width at Top -->
        <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-6">
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
            <h3 class="text-xl font-semibold flex items-center">
              <i class="fas fa-user-tie mr-3"></i>
              Personal Information
            </h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <!-- Profile Image and Basic Info -->
              <div class="lg:col-span-1">
                <div class="flex flex-col items-center">
                  <div class="relative mb-4">
                    <div id="modal-profile-image-container" class="w-32 h-32 rounded-full bg-blue-600 border-4 border-blue-600 shadow-lg overflow-hidden flex items-center justify-center">
                      <div id="modal-profile-image-placeholder" class="text-3xl font-bold text-white">T</div>
                      <img id="modal-profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                    </div>
                  </div>
                  <h4 id="modal-teacher-name" class="text-xl font-bold text-gray-800 text-center mb-2">Loading...</h4>
                  <p id="modal-teacher-designation" class="text-blue-600 font-semibold text-center mb-2">Teacher</p>
                  <span id="modal-teacher-department" class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                    Academic Department
                  </span>
                </div>
              </div>

              <!-- Basic Information -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Basic Information</h5>
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-id-badge text-blue-600 w-5"></i>
                    <span id="modal-teacher-employee-id" class="ml-3 text-gray-700">EMP0001</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-envelope text-blue-600 w-5"></i>
                    <span id="modal-teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-phone text-blue-600 w-5"></i>
                    <span id="modal-teacher-phone" class="ml-3 text-gray-700">+91-XXXXXXXXXX</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar text-blue-600 w-5"></i>
                    <span id="modal-teacher-joining-date" class="ml-3 text-gray-700">Joining Date</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-briefcase text-blue-600 w-5"></i>
                    <span id="modal-teacher-employment-type" class="ml-3 text-gray-700">Employment Type</span>
                  </div>
                </div>
              </div>

              <!-- Personal Details -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Personal Details</h5>
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-birthday-cake text-blue-600 w-5"></i>
                    <span id="modal-date-of-birth" class="ml-3 text-gray-700">Date of Birth</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-venus-mars text-blue-600 w-5"></i>
                    <span id="modal-gender" class="ml-3 text-gray-700">Gender</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-chalkboard-teacher text-blue-600 w-5"></i>
                    <span id="modal-subjects-taught" class="ml-3 text-gray-700">Subjects Taught</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-users text-blue-600 w-5"></i>
                    <span id="modal-classes-handled" class="ml-3 text-gray-700">Classes Handled</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-user text-blue-600 w-5"></i>
                    <span id="modal-username" class="ml-3 text-gray-700">Username</span>
                  </div>
                </div>
              </div>

              <!-- Account & Experience Summary -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Account & Experience</h5>
                <div class="space-y-3 text-sm mb-4">
                  <div class="flex items-center">
                    <i class="fas fa-check-circle text-blue-600 w-5"></i>
                    <span id="modal-account-status" class="ml-3 text-gray-700">Account Status</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-clock text-blue-600 w-5"></i>
                    <span id="modal-last-login" class="ml-3 text-gray-700">Last Login</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar-plus text-blue-600 w-5"></i>
                    <span id="modal-account-created" class="ml-3 text-gray-700">Account Created</span>
                  </div>
                </div>

                <!-- Experience Stats -->
                <div class="bg-blue-50 rounded-lg p-3">
                  <h6 class="font-semibold text-blue-800 mb-2 text-xs uppercase tracking-wide">Experience Summary</h6>
                  <div class="grid grid-cols-2 gap-2 text-center">
                    <div>
                      <div id="modal-total-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-blue-600">Total Years</div>
                    </div>
                    <div>
                      <div id="modal-teaching-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-blue-600">Teaching Years</div>
                    </div>
                  </div>
                  <div class="mt-2 text-center">
                    <div id="modal-administrative-experience" class="text-sm font-bold text-blue-600">0</div>
                    <div class="text-xs text-blue-600">Admin Years</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="space-y-4">
          <!-- Educational Timeline - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-graduation-cap mr-3"></i>
                Educational Timeline
              </h3>
            </div>
            <div class="p-3">
              <div class="relative overflow-x-auto">
                <div class="flex space-x-4 min-w-max pb-2">
                  <div id="modal-education-timeline" class="flex space-x-4">
                    <!-- Timeline entries will be populated here horizontally -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Professional Experience Timeline - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-briefcase mr-3"></i>
                Professional Experience Timeline
              </h3>
              <p class="text-purple-100 text-sm mt-1">Complete work history including previous organizations and current position</p>
            </div>
            <div class="p-3">
              <div class="relative overflow-x-auto">
                <div class="flex space-x-4 min-w-max pb-2">
                  <div id="modal-experience-timeline" class="flex space-x-4">
                    <!-- Timeline entries will be populated here horizontally -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Two Column Grid for Contact/Admin and Publications/Certifications -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Contact & Administrative Details -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-address-book mr-3"></i>
                  Contact & Administrative
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-4">
                  <!-- Contact Information -->
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Contact Information</h5>
                    <div class="space-y-1 text-sm">
                      <div class="flex items-center">
                        <i class="fas fa-phone text-indigo-600 w-4"></i>
                        <span id="modal-alternate-phone" class="ml-2 text-gray-700">Alternate Phone</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-phone-alt text-indigo-600 w-4"></i>
                        <span id="modal-emergency-contact" class="ml-2 text-gray-700">Emergency Contact</span>
                      </div>
                      <div class="flex items-start">
                        <i class="fas fa-map-marker-alt text-indigo-600 w-4 mt-1"></i>
                        <div class="ml-2 text-gray-700">
                          <div id="modal-address" class="mb-1">Address</div>
                          <div class="text-xs text-gray-600">
                            <span id="modal-city">City</span>, <span id="modal-state">State</span> - <span id="modal-pincode">Pincode</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Administrative Details -->
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Administrative Details</h5>
                    <div class="space-y-1 text-sm">
                      <div class="flex items-center">
                        <i class="fas fa-building text-indigo-600 w-4"></i>
                        <span id="modal-office-location" class="ml-2 text-gray-700">Office Location</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-calendar-check text-indigo-600 w-4"></i>
                        <span id="modal-confirmation-date" class="ml-2 text-gray-700">Confirmation Date</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-arrow-up text-indigo-600 w-4"></i>
                        <span id="modal-last-promotion" class="ml-2 text-gray-700">Last Promotion</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-star text-indigo-600 w-4"></i>
                        <span id="modal-performance-rating" class="ml-2 text-gray-700">Performance Rating</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-rupee-sign text-indigo-600 w-4"></i>
                        <span id="modal-current-salary" class="ml-2 text-gray-700">Current Salary</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Publications & Research -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-book mr-3"></i>
                  Publications & Research
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Publications</h5>
                    <div id="modal-publications-list" class="text-sm text-gray-600">
                      <!-- Publications will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Research Papers</h5>
                    <div id="modal-research-papers-list" class="text-sm text-gray-600">
                      <!-- Research papers will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Conferences</h5>
                    <div id="modal-conferences-list" class="text-sm text-gray-600">
                      <!-- Conferences will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Two Column Grid for Certifications and Skills -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Professional Certifications -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-pink-600 to-pink-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-certificate mr-3"></i>
                  Certifications & Qualifications
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Professional Certifications</h5>
                    <div id="modal-certifications-list" class="text-sm text-gray-600">
                      <!-- Certifications will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Other Qualifications</h5>
                    <div id="modal-other-qualifications-list" class="text-sm text-gray-600">
                      <!-- Other qualifications will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Skills Section -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-cogs mr-3"></i>
                  Skills & Languages
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Special Skills</h5>
                    <div id="modal-skills-list" class="text-sm text-gray-600">
                      <!-- Skills will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Languages Known</h5>
                    <div id="modal-languages-list" class="text-sm text-gray-600">
                      <!-- Languages will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Comprehensive Achievements Section - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-trophy mr-3"></i>
                Achievements & Recognition
              </h3>
              <p class="text-yellow-100 text-sm mt-1">Professional accomplishments and awards</p>
            </div>
            <div class="p-3">
              <div id="modal-achievements-content">
                <!-- Achievements will be populated here -->
              </div>
            </div>
          </div>

          <!-- Additional Notes - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-gray-600 to-gray-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-sticky-note mr-3"></i>
                Additional Notes
              </h3>
            </div>
            <div class="p-3">
              <div id="modal-notes" class="text-sm text-gray-600">
                <!-- Notes will be populated here -->
              </div>
            </div>
          </div>
        </div>
          </div>
        </div>

        <!-- Modal Footer - Moved inside content area -->
        <div id="teacherModalFooter" class="bg-gray-50 px-6 py-4 mt-6 rounded-lg flex justify-end space-x-3 border-t border-gray-200">
          <button id="closeTeacherModalBtn2" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
          <button id="printTeacherProfile" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors" data-action="generate-cv">
            <i class="fas fa-file-pdf mr-2"></i>Generate CV PDF
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom CSS for compact layout -->
<style>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Compact modal spacing */
.enhanced-achievements-section .achievement-category {
    margin-bottom: 1rem;
}

/* Responsive grid improvements */
@media (min-width: 1024px) {
    .achievement-category .grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* Smooth transitions */
.achievement-category > div {
    transition: all 0.2s ease-in-out;
}

/* Compact timeline spacing */
.flex.space-x-4 {
    gap: 1rem;
}

.flex.space-x-6 {
    gap: 1.5rem;
}

/* Modal content optimization */
#enhanced-profile-content .space-y-4 > * + * {
    margin-top: 1rem;
}

#enhanced-profile-content .space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Ensure full width utilization */
.modal-content-container {
    max-width: none;
    width: 95vw;
}

/* Modal footer styling */
#teacherModalFooter {
    margin-top: 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

/* Make sure PDF button is visible and styled */
#printTeacherProfile {
    min-width: 150px;
    font-weight: 500;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: #2563eb !important;
    color: white !important;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 20;
}

#printTeacherProfile:hover {
    background-color: #1d4ed8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Ensure modal footer is always visible */
#teacherModalFooter {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content-container {
        width: 98vw;
        margin: 0.5rem;
    }

    .achievement-category .grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- All JavaScript functionality has been moved to external files for better organization -->

<!-- CV Generator Script -->
<script src="/js/cv-generator.js"></script>

<!-- Principal Timetable Script -->
<script>
// Principal Timetable Management
$(document).ready(function() {
    console.log('🎯 Principal Timetable initializing...');

    // Time slots configuration
    const timeSlots = [
        { start: '08:00', end: '08:40', period: 1 },
        { start: '08:50', end: '09:30', period: 2 },
        { start: '09:40', end: '10:20', period: 3 },
        { start: '10:30', end: '11:10', period: 4 },
        { start: '11:20', end: '12:00', period: 5 },
        { start: '12:10', end: '12:50', period: 6 },
        { start: '13:00', end: '13:40', period: 7 },
        { start: '13:50', end: '14:30', period: 8 }
    ];

    // Update current time display
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: true,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        $('#current-time').text(timeString);
    }

    // Check if current time is within a time slot
    function isCurrentTimeSlot(startTime, endTime) {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();

        const [startHour, startMin] = startTime.split(':').map(Number);
        const [endHour, endMin] = endTime.split(':').map(Number);

        const slotStart = startHour * 60 + startMin;
        const slotEnd = endHour * 60 + endMin;

        return currentTime >= slotStart && currentTime <= slotEnd;
    }

    // Load enhanced timetable data
    async function loadTimetableData() {
        try {
            console.log('📊 Loading enhanced timetable data...');

            const today = new Date().toISOString().split('T')[0];
            const response = await fetch(`/principal/api/timetable?date=${today}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ Enhanced timetable data loaded:', data);

            if (data.success) {
                // Update stats in header if available
                if (data.stats) {
                    updateTimetableStats(data.stats);
                }

                // Render timetable with enhanced data
                renderTimetable(data.lectures || [], data.timeSlots);

                // Update last refresh time
                const now = new Date();
                console.log(`🔄 Timetable refreshed at ${now.toLocaleTimeString()}`);
            } else {
                throw new Error(data.message || 'Failed to load timetable data');
            }

        } catch (error) {
            console.error('❌ Error loading timetable:', error);
            renderEmptyTimetable();

            // Show error notification
            showNotification('Failed to load timetable data. Please try again.', 'error');
        }
    }

    // Update timetable statistics
    function updateTimetableStats(stats) {
        console.log('📈 Updating timetable stats:', stats);

        // You can add stats display elements to the header if needed
        // For now, just log the stats
        if (stats.activeLectures > 0) {
            console.log(`🔴 ${stats.activeLectures} lectures currently active`);
        }
        if (stats.completedLectures > 0) {
            console.log(`✅ ${stats.completedLectures} lectures completed today`);
        }
        if (stats.upcomingLectures > 0) {
            console.log(`🔵 ${stats.upcomingLectures} lectures upcoming`);
        }
    }

    // Show notification helper
    function showNotification(message, type = 'info') {
        // Simple notification - you can enhance this with a proper notification system
        const notification = $(`
            <div class="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'success' ? 'bg-green-500 text-white' :
                'bg-blue-500 text-white'
            }">
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'error' ? 'fa-exclamation-triangle' :
                        type === 'success' ? 'fa-check-circle' :
                        'fa-info-circle'
                    } mr-2"></i>
                    <span>${message}</span>
                </div>
            </div>
        `);

        $('body').append(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.fadeOut(300, () => notification.remove());
        }, 5000);
    }

    // Render enhanced timetable with real data
    function renderTimetable(lectures, timeSlots) {
        const tbody = $('#timetable-body');
        tbody.empty();

        console.log('🎨 Rendering timetable with', lectures.length, 'lectures');

        // Use provided timeSlots or default ones
        const slots = timeSlots || [
            { start: '08:00:00', end: '08:40:00', period: 1 },
            { start: '08:45:00', end: '09:25:00', period: 2 },
            { start: '09:30:00', end: '10:10:00', period: 3 },
            { start: '10:15:00', end: '10:55:00', period: 4 },
            { start: '11:00:00', end: '11:40:00', period: 5 },
            { start: '11:45:00', end: '12:25:00', period: 6 },
            { start: '12:30:00', end: '13:10:00', period: 7 },
            { start: '13:15:00', end: '13:55:00', period: 8 }
        ];

        // Group lectures by class/room
        const lecturesByClass = {};
        lectures.forEach(lecture => {
            const key = lecture.class_name || 'Unknown Class';
            if (!lecturesByClass[key]) {
                lecturesByClass[key] = {};
            }
            // Use slot_index or calculate from period
            const periodIndex = lecture.slot_index !== null ? lecture.slot_index : (lecture.period || 1) - 1;
            lecturesByClass[key][periodIndex + 1] = lecture; // Convert to 1-based for display
        });

        // If no lectures found, show all classes with empty periods
        if (Object.keys(lecturesByClass).length === 0) {
            // Create sample classes for display
            const sampleClasses = [
                '11 Non-Medical A', '11 Non-Medical B', '11 Medical A',
                '12 Non-Medical A', '12 Commerce A'
            ];

            sampleClasses.forEach(className => {
                lecturesByClass[className] = {};
            });
        }

        // Create rows for each class
        Object.keys(lecturesByClass).sort().forEach(className => {
            const row = $('<tr></tr>');

            // Class name column with enhanced styling
            row.append(`
                <td class="px-4 py-3 font-medium text-gray-900 bg-gray-50 border-r border-gray-200">
                    <div class="flex items-center">
                        <i class="fas fa-users text-blue-600 mr-2"></i>
                        <span>${className}</span>
                    </div>
                </td>
            `);

            // Period columns
            for (let period = 1; period <= 8; period++) {
                const lecture = lecturesByClass[className][period];
                const timeSlot = slots[period - 1];

                let cellContent = '';
                let cellClass = 'px-3 py-3 text-sm border-r border-gray-100 relative';

                if (lecture) {
                    // Determine status based on currentStatus or calculate it
                    let status = lecture.currentStatus;
                    if (!status) {
                        const isActive = isCurrentTimeSlot(timeSlot.start, timeSlot.end);
                        const now = new Date();
                        const lectureEnd = new Date(`${lecture.date}T${lecture.end_time}`);

                        if (isActive) {
                            status = 'active';
                        } else if (now > lectureEnd) {
                            status = 'completed';
                        } else {
                            status = 'upcoming';
                        }
                    }

                    // Apply styling based on status
                    if (status === 'active') {
                        cellClass += ' bg-red-50 border-2 border-red-400 text-red-900';
                    } else if (status === 'completed') {
                        cellClass += ' bg-green-50 border-l-4 border-green-400 text-green-900';
                    } else if (status === 'cancelled') {
                        cellClass += ' bg-gray-50 border-l-4 border-gray-400 text-gray-700';
                    } else {
                        cellClass += ' bg-blue-50 border-l-4 border-blue-400 text-blue-900';
                    }

                    // Format teacher name
                    const teacherName = lecture.teacher_full_name || lecture.teacher_name || 'Teacher';
                    const shortTeacherName = teacherName.length > 15 ?
                        teacherName.substring(0, 12) + '...' : teacherName;

                    cellContent = `
                        <div class="space-y-1">
                            <div class="font-semibold text-sm leading-tight">
                                ${lecture.subject_name || 'Subject'}
                            </div>
                            <div class="text-xs text-gray-700 flex items-center">
                                <i class="fas fa-user-tie mr-1"></i>
                                ${shortTeacherName}
                            </div>
                            <div class="text-xs text-gray-600 flex items-center">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                ${lecture.location || 'Classroom'}
                            </div>
                            <div class="text-xs font-medium">
                                ${timeSlot.start.substring(0,5)} - ${timeSlot.end.substring(0,5)}
                            </div>
                            ${status === 'active' ?
                                '<div class="absolute top-1 right-1"><span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-600 text-white animate-pulse">● LIVE</span></div>' :
                                status === 'completed' ?
                                '<div class="absolute top-1 right-1"><span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-600 text-white"><i class="fas fa-check"></i></span></div>' :
                                status === 'cancelled' ?
                                '<div class="absolute top-1 right-1"><span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-600 text-white"><i class="fas fa-times"></i></span></div>' :
                                ''
                            }
                            ${lecture.topic ? `<div class="text-xs text-gray-500 italic">${lecture.topic.substring(0, 20)}${lecture.topic.length > 20 ? '...' : ''}</div>` : ''}
                        </div>
                    `;
                } else {
                    cellClass += ' bg-gray-50 text-gray-400 text-center';
                    cellContent = `
                        <div class="py-2">
                            <div class="text-xs text-gray-400 mb-1">
                                ${timeSlot.start.substring(0,5)} - ${timeSlot.end.substring(0,5)}
                            </div>
                            <div class="text-xs font-medium text-gray-500">NA</div>
                            <div class="text-xs text-gray-400">Free Period</div>
                        </div>
                    `;
                }

                row.append(`<td class="${cellClass}">${cellContent}</td>`);
            }

            tbody.append(row);
        });

        // Add summary row if there are lectures
        if (lectures.length > 0) {
            const activeLectures = lectures.filter(l => l.currentStatus === 'active').length;
            const completedLectures = lectures.filter(l => l.currentStatus === 'completed').length;
            const upcomingLectures = lectures.filter(l => l.currentStatus === 'upcoming').length;
            const cancelledLectures = lectures.filter(l => l.currentStatus === 'cancelled').length;

            const summaryRow = $(`
                <tr class="bg-gradient-to-r from-blue-50 to-indigo-50 border-t-2 border-blue-200">
                    <td class="px-4 py-3 font-bold text-blue-900">
                        <i class="fas fa-chart-bar mr-2"></i>Summary
                    </td>
                    <td colspan="8" class="px-4 py-3">
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex space-x-6">
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                    <strong>${activeLectures}</strong> Active
                                </span>
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <strong>${completedLectures}</strong> Completed
                                </span>
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <strong>${upcomingLectures}</strong> Upcoming
                                </span>
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                                    <strong>${cancelledLectures}</strong> Cancelled
                                </span>
                            </div>
                            <div class="text-blue-700 font-medium">
                                Total: <strong>${lectures.length}</strong> lectures today
                            </div>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(summaryRow);
        }

        console.log('✅ Timetable rendered successfully');
    }

    // Render empty timetable
    function renderEmptyTimetable() {
        const tbody = $('#timetable-body');
        tbody.empty();

        const row = $('<tr></tr>');
        row.append(`
            <td colspan="9" class="px-4 py-8 text-center text-gray-500">
                <i class="fas fa-calendar-times text-3xl mb-2"></i>
                <div>No classes scheduled for today</div>
            </td>
        `);
        tbody.append(row);
    }

    // Manual refresh button handler
    $('#refreshTimetable').on('click', function() {
        const button = $(this);
        const originalHtml = button.html();

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');
        button.prop('disabled', true);

        // Load data
        loadTimetableData().finally(() => {
            // Restore button state
            setTimeout(() => {
                button.html(originalHtml);
                button.prop('disabled', false);
            }, 1000);
        });
    });

    // Initialize
    updateCurrentTime();
    loadTimetableData();

    // Update time every second
    setInterval(updateCurrentTime, 1000);

    // Refresh timetable every 2 minutes for real-time updates
    setInterval(loadTimetableData, 2 * 60 * 1000);

    console.log('✅ Enhanced Principal Timetable initialized successfully');
});
</script>

<!-- DIRECT PDF BUTTON HANDLERS - SIMPLE APPROACH -->
<script>
console.log('🚀 Loading direct PDF handlers...');

// Direct button click handlers - no data attributes needed
$(document).ready(function() {
    console.log('✅ Document ready - setting up direct handlers');

    // Test PDF button - direct selector
    $('.pdf-action-btn, [data-action="test-pdf"]').on('click', function(e) {
        e.preventDefault();
        console.log('� TEST PDF BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...');

        setTimeout(() => {
            try {
                const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Test PDF</title>
                    <style>
                        @page { size: A4; margin: 20mm; }
                        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                        h1 { color: #2563eb; }
                        .content { line-height: 1.6; }
                        @media print { .no-print { display: none; } }
                    </style>
                </head>
                <body>
                    <div class='no-print' style='background: #f0f0f0; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
                        <h3>✅ PDF Test Document</h3>
                        <p>Use <strong>Ctrl+P</strong> or <strong>Cmd+P</strong> to print this as PDF</p>
                        <button onclick='window.print()' style='background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                        <button onclick='window.close()' style='background: #666; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                    </div>
                    <div class='content'>
                        <h1>✅ PDF Generation Test SUCCESS</h1>
                        <p><strong>Generated at:</strong> ${new Date().toLocaleString()}</p>
                        <p>This confirms PDF functionality is working perfectly!</p>
                        <p><strong>System:</strong> Teacher Management System</p>
                        <p><strong>Test Status:</strong> ✅ SUCCESS</p>
                        <hr>
                        <p><em>This document was generated using the browser's native print-to-PDF functionality.</em></p>
                    </div>
                </body>
                </html>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(htmlContent);
                printWindow.document.close();

                button.html('<i class="fas fa-check mr-2"></i>PDF Opened!');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);

                console.log('✅ Test PDF generated successfully!');

            } catch(error) {
                console.error('❌ Test PDF error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 500);
    });

    // CV Generation buttons - multiple selectors
    $('.generate-cv-btn, .cv-generate-btn, [data-action="generate-cv"]').on('click', function(e) {
        e.preventDefault();
        console.log('🔥 CV GENERATION BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating CV...');

        setTimeout(() => {
            try {
                // Get teacher data
                let teacher;
                const teacherId = button.data('teacher-id');

                if (window.currentTeacherData) {
                    teacher = window.currentTeacherData;
                    console.log('✅ Using cached teacher data');
                } else if (teacherId) {
                    // Get data from table row
                    const teacherRow = button.closest('tr');
                    const teacherName = teacherRow.find('.text-sm.font-medium').first().text().trim();
                    const teacherEmail = teacherRow.find('.text-sm.text-gray-500').first().text().trim();

                    teacher = {
                        id: teacherId,
                        name: teacherName || 'Teacher Name',
                        email: teacherEmail || '<EMAIL>',
                        designation: 'Teacher',
                        department: 'Academic Department',
                        employee_id: `EMP${String(teacherId).padStart(4, '0')}`,
                        joining_date: new Date().toISOString().split('T')[0],
                        employment_type: 'Permanent',
                        account_status: 'Active'
                    };
                    console.log('✅ Created teacher object from table:', teacher);
                } else {
                    // Default teacher data
                    teacher = {
                        name: 'Sample Teacher',
                        designation: 'Teacher',
                        department: 'Academic Department',
                        employee_id: 'EMP0001',
                        email: '<EMAIL>',
                        joining_date: new Date().toISOString().split('T')[0],
                        employment_type: 'Permanent',
                        account_status: 'Active'
                    };
                    console.log('✅ Using default teacher data');
                }

                // Generate CV HTML
                const cvHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${teacher.name || 'Teacher'} - Curriculum Vitae</title>
                    <style>
                        @page { size: A4; margin: 15mm; }
                        body { font-family: 'Times New Roman', serif; margin: 0; padding: 0; line-height: 1.4; color: #333; }
                        .header { text-align: center; border-bottom: 3px solid #2563eb; padding-bottom: 15px; margin-bottom: 20px; }
                        .header h1 { color: #2563eb; margin: 0; font-size: 24px; }
                        .header h2 { color: #666; margin: 5px 0; font-size: 16px; font-weight: normal; }
                        .section { margin-bottom: 20px; }
                        .section-title { background: #2563eb; color: white; padding: 8px 15px; margin-bottom: 10px; font-weight: bold; }
                        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
                        .info-item { margin-bottom: 8px; }
                        .info-label { font-weight: bold; color: #2563eb; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                        @media print { .no-print { display: none; } }
                    </style>
                </head>
                <body>
                    <div class='no-print' style='background: #e3f2fd; padding: 15px; margin-bottom: 20px; border-radius: 5px; border: 1px solid #2196f3;'>
                        <h3 style='margin: 0 0 10px 0; color: #1976d2;'>✅ Teacher CV Document</h3>
                        <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
                        <button onclick='window.print()' style='background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                        <button onclick='window.close()' style='background: #666; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                    </div>
                    <div class='header'>
                        <h1>${teacher.name || 'Teacher Name'}</h1>
                        <h2>${teacher.designation || 'Teacher'}</h2>
                        <p>${teacher.department || 'Academic Department'}</p>
                    </div>
                    <div class='section'>
                        <div class='section-title'>PERSONAL INFORMATION</div>
                        <div class='info-grid'>
                            <div>
                                <div class='info-item'><span class='info-label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Email:</span> ${teacher.email || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Date of Birth:</span> ${teacher.date_of_birth || 'N/A'}</div>
                            </div>
                            <div>
                                <div class='info-item'><span class='info-label'>Gender:</span> ${teacher.gender || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Employment Type:</span> ${teacher.employment_type || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Account Status:</span> ${teacher.account_status || 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                    <div class='section'>
                        <div class='section-title'>PROFESSIONAL EXPERIENCE</div>
                        <div class='info-item'><span class='info-label'>Total Experience:</span> ${teacher.total_experience_years || 0} years</div>
                        <div class='info-item'><span class='info-label'>Teaching Experience:</span> ${teacher.teaching_experience_years || 0} years</div>
                        <div class='info-item'><span class='info-label'>Subjects Taught:</span> ${teacher.subjects_taught || 'N/A'}</div>
                    </div>
                    <div class='section'>
                        <div class='section-title'>ADMINISTRATIVE DETAILS</div>
                        <div class='info-grid'>
                            <div>
                                <div class='info-item'><span class='info-label'>Office Location:</span> ${teacher.office_location || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Performance Rating:</span> ${teacher.performance_rating || 'N/A'}</div>
                            </div>
                            <div>
                                <div class='info-item'><span class='info-label'>Confirmation Date:</span> ${teacher.confirmation_date || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Last Promotion:</span> ${teacher.last_promotion || 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                    <div class='footer'>
                        <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                        <p>Teacher Management System - Curriculum Vitae</p>
                    </div>
                </body>
                </html>
                `;

                const cvWindow = window.open('', '_blank');
                cvWindow.document.write(cvHTML);
                cvWindow.document.close();

                button.html('<i class="fas fa-check mr-2"></i>CV Generated!');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);

                console.log('✅ CV generated successfully!');

            } catch(error) {
                console.error('❌ CV generation error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 500);
    });

    // Refresh button handler
    $('.action-btn[data-action="refresh"], [data-action="refresh"]').on('click', function(e) {
        e.preventDefault();
        console.log('🔥 REFRESH BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');

        setTimeout(() => {
            location.reload();
        }, 500);
    });

    // Debug: List all buttons found
    setTimeout(() => {
        console.log('🔍 BUTTON DETECTION REPORT:');
        console.log('PDF Action buttons:', $('.pdf-action-btn').length);
        console.log('Generate CV buttons:', $('.generate-cv-btn').length);
        console.log('CV Generate buttons:', $('.cv-generate-btn').length);
        console.log('Data action test-pdf:', $('[data-action="test-pdf"]').length);
        console.log('Data action generate-cv:', $('[data-action="generate-cv"]').length);
        console.log('Data action refresh:', $('[data-action="refresh"]').length);

        // Test manual trigger
        window.triggerTestPDF = function() {
            console.log('🧪 Manual trigger test PDF');
            $('.pdf-action-btn, [data-action="test-pdf"]').first().trigger('click');
        };

        window.triggerCVGeneration = function() {
            console.log('🧪 Manual trigger CV generation');
            $('.generate-cv-btn, .cv-generate-btn, [data-action="generate-cv"]').first().trigger('click');
        };

        console.log('✅ Manual trigger functions available: triggerTestPDF(), triggerCVGeneration()');
    }, 1000);
});
</script>

<!-- Simple PDF Library - Local fallback for reliable PDF generation -->
<script src="/js/jspdf-simple.js"></script>
<script>
// Ensure jsPDF is available globally
if (typeof window.jsPDF === 'function') {
    window.jsPDF = window.jsPDF;
    // Also make it available without window prefix
    if (typeof jsPDF === 'undefined') {
        var jsPDF = window.jsPDF;
    }
    console.log('✅ jsPDF made globally available');
} else {
    console.error('❌ window.jsPDF not found after loading library');
}
</script>
<script src="/js/enhanced-teacher-modal-combined.js?v=1748751732798"></script>
<script src="/js/teacher-profile-pdf-generator.js"></script>

<!-- Simple test PDF function - defined globally -->
<script>
// Check jsPDF library availability
function checkJsPDFAvailability() {
    console.log('🔍 Checking jsPDF availability...');
    console.log('window.jspdf:', typeof window.jspdf);
    console.log('window.jsPDF:', typeof window.jsPDF);
    console.log('window.jspdf?.jsPDF:', typeof window.jspdf?.jsPDF);

    if (window.jspdf && window.jspdf.jsPDF) {
        console.log('✅ jsPDF available via window.jspdf.jsPDF');
        return window.jspdf.jsPDF;
    } else if (window.jsPDF) {
        console.log('✅ jsPDF available via window.jsPDF');
        return window.jsPDF;
    } else {
        console.log('❌ jsPDF not available');
        return null;
    }
}

// Simple test PDF function - GLOBAL SCOPE
function testPDFClick() {
    console.log('🚨 INLINE ONCLICK TRIGGERED!');
    alert('Inline onclick works! Now testing PDF...');

    try {
        // Check if jsPDF is available
        console.log('Checking jsPDF...');
        console.log('window.jspdf:', typeof window.jspdf);
        console.log('window.jsPDF:', typeof window.jsPDF);

        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
            console.log('Using window.jspdf.jsPDF');
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
            console.log('Using window.jsPDF');
        } else {
            alert('jsPDF library not found!');
            return;
        }

        // Create simple PDF
        console.log('Creating PDF...');
        const doc = new jsPDFClass();
        doc.text('Test PDF from inline function', 20, 20);
        doc.text('Date: ' + new Date().toLocaleString(), 20, 40);
        doc.save('inline-test.pdf');

        console.log('✅ PDF created successfully!');
        alert('PDF generated successfully!');
    } catch (error) {
        console.error('❌ Error:', error);
        alert('Error: ' + error.message);
    }
}

$(document).ready(function() {
    console.log('Teacher management page loaded');
    console.log('Enhanced modal functions available:', typeof window.openEnhancedTeacherModal);

    // Check jsPDF library on page load
    setTimeout(function() {
        console.log('🔍 Checking jsPDF after page load...');
        console.log('window.jsPDF:', typeof window.jsPDF);
        console.log('window.SimplePDF:', typeof window.SimplePDF);

        // Test if we can create an instance
        try {
            const testDoc = new jsPDF();
            console.log('✅ jsPDF constructor works:', testDoc);
        } catch(e) {
            console.error('❌ jsPDF constructor failed:', e);
        }

        checkJsPDFAvailability();
    }, 1000);

    // Debug button existence
    console.log('🔍 Checking buttons on page load:');
    console.log('Test PDF button exists:', $('#testPDFBtn').length);
    console.log('Test PDF button HTML:', $('#testPDFBtn').html());

    // Add immediate click handler for testing
    $('#testPDFBtn').click(function() {
        console.log('🚨 JQUERY CLICK HANDLER TRIGGERED!');

        try {
            if (window.jspdf && window.jspdf.jsPDF) {
                const doc = new window.jspdf.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else if (window.jsPDF) {
                const doc = new window.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else {
                alert('jsPDF not loaded in jQuery handler');
            }
        } catch(e) {
            console.error('jQuery PDF error:', e);
            alert('jQuery Error: ' + e.message);
        }
    });

    // Test if jQuery is working at all
    console.log('jQuery version:', $.fn.jquery);
    console.log('Document ready fired');

    // Add a simple test button click
    setTimeout(function() {
        console.log('🔍 Delayed check - Test PDF button exists:', $('#testPDFBtn').length);
        if ($('#testPDFBtn').length > 0) {
            console.log('✅ Test PDF button found after delay');
        } else {
            console.log('❌ Test PDF button NOT found after delay');
        }
    }, 1000);

    // Add event listeners for view teacher buttons
    $('.view-teacher-btn').on('click', function(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        if (window.openEnhancedTeacherModal) {
            console.log('Calling enhanced modal for teacher:', teacherId);
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Enhanced modal not loaded. Please refresh the page.');
        }
    });

    // Add event listeners for modal close buttons
    $('#closeTeacherModalBtn, #closeTeacherModalBtn2').on('click', function() {
        console.log('Close button clicked');
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // Close modal when clicking outside
    $('#teacherModal').on('click', function(e) {
        if (e.target === this) {
            console.log('Clicked outside modal, closing');
            if (window.closeEnhancedTeacherModal) {
                window.closeEnhancedTeacherModal();
            }
        }
    });

    // Check if PDF button exists
    console.log('PDF button exists:', $('#printTeacherProfile').length);
    console.log('Modal footer exists:', $('#teacherModalFooter').length);

    // PDF Generation functionality using event delegation
    $(document).on('click', '#printTeacherProfile', function() {
        console.log('🔄 PDF generation button clicked via event delegation');

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');
        button.prop('disabled', true);

        // Call our global function
        setTimeout(async () => {
            try {
                if (typeof window.generateTeacherCVPDF === 'function') {
                    await window.generateTeacherCVPDF();

                    // Show success message
                    button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        button.html(originalText);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('generateTeacherCVPDF function not available');
                }
            } catch (error) {
                console.error('PDF generation error:', error);
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    // Test PDF button functionality using event delegation
    $(document).on('click', '#testPDFBtn', function() {
        console.log('🔄 Test PDF button clicked');

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing PDF...');
        button.prop('disabled', true);

        // Check library availability first
        console.log('🔍 Checking PDF libraries...');
        console.log('jsPDF available:', typeof window.jspdf);
        console.log('jsPDF global:', typeof window.jsPDF);
        console.log('PDF generator function:', typeof window.generateTeacherProfilePDF);

        // Create sample teacher data for testing
        const sampleTeacher = {
            name: 'Test Teacher',
            designation: 'Computer Science Teacher',
            department: 'Computer Science',
            employee_id: 'TEST001',
            email: '<EMAIL>',
            phone: '+91-9876543210',
            joining_date: '2020-01-15',
            employment_type: 'Permanent',
            date_of_birth: '1985-05-20',
            gender: 'Male',
            subjects_taught: 'Computer Science, Programming',
            total_experience_years: 8,
            teaching_experience_years: 6,
            address: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            pincode: '123456'
        };

        setTimeout(() => {
            try {
                // Check if PDF generator is available
                if (typeof window.generateTeacherProfilePDF !== 'function') {
                    throw new Error('PDF generator function not loaded');
                }

                // Check if jsPDF is available
                if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
                    throw new Error('jsPDF library not loaded');
                }

                console.log('✅ All checks passed, generating PDF...');

                // Try simple jsPDF test first
                try {
                    let jsPDFClass;
                    if (window.jspdf && window.jspdf.jsPDF) {
                        jsPDFClass = window.jspdf.jsPDF;
                    } else if (window.jsPDF) {
                        jsPDFClass = window.jsPDF;
                    } else {
                        throw new Error('jsPDF not available');
                    }

                    console.log('🔄 Creating simple test PDF...');
                    const doc = new jsPDFClass();
                    doc.text('Test PDF Generation', 20, 20);
                    doc.text('Teacher: ' + sampleTeacher.name, 20, 40);
                    doc.text('Department: ' + sampleTeacher.department, 20, 60);
                    doc.save('test-pdf.pdf');
                    console.log('✅ Simple PDF test successful');

                    // Now try full PDF generation
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                } catch (simpleError) {
                    console.error('❌ Simple PDF test failed:', simpleError);
                    // Try the full generator anyway
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                }
            } catch (error) {
                console.error('❌ PDF generation error:', error);
                alert(`PDF generation failed: ${error.message}`);
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    console.log('Event listeners attached');
});

// Simple PDF generation function that can be called directly
window.generateSimplePDF = function() {
    console.log('🔄 Direct PDF generation called');

    try {
        // Check if jsPDF is available
        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
        } else {
            alert('jsPDF library not loaded');
            return false;
        }

        // Create simple PDF
        const doc = new jsPDFClass();
        doc.text('Simple PDF Test', 20, 20);
        doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
        doc.text('This confirms PDF generation is working!', 20, 60);
        doc.save('simple-test.pdf');

        console.log('✅ Simple PDF generated successfully');
        return true;
    } catch (error) {
        console.error('❌ Simple PDF generation failed:', error);
        alert('PDF generation failed: ' + error.message);
        return false;
    }
};

// Function to generate teacher PDF directly
window.generateTeacherPDF = function() {
    console.log('🔄 Direct teacher PDF generation called');

    if (!window.currentTeacherData) {
        alert('No teacher data available. Please open a teacher profile first.');
        return false;
    }

    if (typeof window.generateTeacherProfilePDF === 'function') {
        return window.generateTeacherProfilePDF(window.currentTeacherData);
    } else {
        alert('PDF generator not loaded');
        return false;
    }
};

// Search and filter functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    location.reload();
}

// Search functionality
$(document).on('input', '#search-teachers', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('#teachers-table-body tr').each(function() {
        const name = $(this).data('name') || '';
        const email = $(this).data('email') || '';
        const isVisible = name.includes(searchTerm) || email.includes(searchTerm);
        $(this).toggle(isVisible);
    });
});

// Filter functionality
$(document).on('change', '#filter-performance', function() {
    const filterValue = $(this).val();
    $('#teachers-table-body tr').each(function() {
        const performance = $(this).data('performance') || '';
        const isVisible = !filterValue || performance === filterValue;
        $(this).toggle(isVisible);
    });
});

// Send message functionality
function sendMessage(teacherId) {
    console.log('Send message to teacher:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
}

// Duplicate function removed - using global scope version above
</script>

