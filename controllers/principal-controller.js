/**
 * Principal Controller
 * Handles all principal dashboard functionality and school oversight
 */

const db = require('../config/database');

// Principal Dashboard
exports.getDashboard = async (req, res) => {
  try {
    console.log('Loading principal dashboard...');

    // Get basic school statistics using only existing tables
    let stats = {
      total_teachers: 0,
      total_students: 0,
      total_classes: 0,
      total_subjects: 0,
      total_lectures_today: 0,
      delivered_lectures: 0,
      pending_lectures: 0,
      cancelled_lectures: 0,
      completion_percentage: 0
    };

    try {
      // Get user counts
      const [userStats] = await db.query(`
        SELECT
          (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
          (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students
      `);

      if (userStats && userStats[0]) {
        stats.total_teachers = userStats[0].total_teachers || 0;
        stats.total_students = userStats[0].total_students || 0;
      }
    } catch (userError) {
      console.log('Error fetching user stats:', userError.message);
    }

    // Try to get additional stats from other tables if they exist
    try {
      const [additionalStats] = await db.query(`
        SELECT
          (SELECT COUNT(*) FROM classes) as total_classes,
          (SELECT COUNT(*) FROM subjects) as total_subjects
      `);

      if (additionalStats && additionalStats[0]) {
        stats.total_classes = additionalStats[0].total_classes || 0;
        stats.total_subjects = additionalStats[0].total_subjects || 0;
      }
    } catch (additionalError) {
      console.log('Classes/subjects tables not found, using defaults');
      stats.total_classes = 12; // Default value
      stats.total_subjects = 15; // Default value
    }

    // Mock data for demonstration
    const recentActivities = [
      {
        type: 'system',
        title: 'System Status Check',
        subtitle: 'All systems operational',
        teacher_name: 'System Admin',
        date: new Date(),
        status: 'completed',
        created_at: new Date()
      },
      {
        type: 'login',
        title: 'Teacher Login Activity',
        subtitle: 'Recent faculty access',
        teacher_name: 'Faculty Members',
        date: new Date(),
        status: 'delivered',
        created_at: new Date()
      }
    ];

    const classPerformance = [
      { class_name: 'Class 12', total_lectures: 45, completed_lectures: 38, completion_rate: 84.4 },
      { class_name: 'Class 11', total_lectures: 42, completed_lectures: 35, completion_rate: 83.3 },
      { class_name: 'Class 10', total_lectures: 40, completed_lectures: 32, completion_rate: 80.0 },
      { class_name: 'Class 9', total_lectures: 38, completed_lectures: 30, completion_rate: 78.9 }
    ];

    const teacherPerformance = [
      { teacher_name: 'Dr. Sharma', total_lectures: 25, delivered_lectures: 23, completion_rate: 92.0 },
      { teacher_name: 'Prof. Kumar', total_lectures: 22, delivered_lectures: 20, completion_rate: 90.9 },
      { teacher_name: 'Ms. Patel', total_lectures: 20, delivered_lectures: 18, completion_rate: 90.0 },
      { teacher_name: 'Mr. Singh', total_lectures: 24, delivered_lectures: 21, completion_rate: 87.5 }
    ];

    const upcomingEvents = [
      {
        type: 'meeting',
        title: 'Faculty Meeting',
        subtitle: 'Monthly Review',
        date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        start_time: '10:00:00',
        class_name: 'Conference Room'
      },
      {
        type: 'event',
        title: 'Parent-Teacher Conference',
        subtitle: 'Academic Discussion',
        date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        start_time: '14:00:00',
        class_name: 'Main Hall'
      }
    ];

    // Set completion percentage based on mock data
    stats.completion_percentage = 82.5;
    stats.total_lectures_today = 15;
    stats.delivered_lectures = 12;
    stats.pending_lectures = 3;

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    console.log('Principal Dashboard - Layout set to:', res.locals.layout);
    console.log('Principal Dashboard - Stats:', stats);

    res.render('principal/dashboard', {
      title: 'Principal Command Center',
      layout: 'layouts/principal',
      currentPage: 'dashboard',
      stats,
      recentActivities,
      classPerformance,
      teacherPerformance,
      upcomingEvents,
      formatDate: (date) => {
        if (!date) return '';
        return new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      },
      formatTime: (time) => {
        if (!time) return '';
        return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      },
      getStatusColor: (status) => {
        const colors = {
          'delivered': 'text-green-600',
          'completed': 'text-green-600',
          'pending': 'text-yellow-600',
          'cancelled': 'text-red-600',
          'overdue': 'text-red-600'
        };
        return colors[status] || 'text-gray-600';
      },
      getStatusBadge: (status) => {
        const badges = {
          'delivered': 'bg-green-100 text-green-800',
          'completed': 'bg-green-100 text-green-800',
          'pending': 'bg-yellow-100 text-yellow-800',
          'cancelled': 'bg-red-100 text-red-800',
          'overdue': 'bg-red-100 text-red-800'
        };
        return badges[status] || 'bg-gray-100 text-gray-800';
      }
    });
  } catch (error) {
    console.error('Error loading principal dashboard:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load principal dashboard',
      error: { status: 500 },
      layout: 'layouts/principal'
    });
  }
};

// Academic Progress
exports.getAcademicProgress = async (req, res) => {
  try {
    // Get class-wise syllabus completion - simplified query
    const [classProgress] = await db.query(`
      SELECT
        tl.class_name,
        tl.subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage,
        u.name as teacher_name
      FROM teacher_lectures tl
      LEFT JOIN users u ON tl.teacher_id = u.id
      GROUP BY tl.class_name, tl.subject_name, u.name
      ORDER BY tl.class_name, tl.subject_name
    `);

    // Get subject-wise progress
    const [subjectProgress] = await db.query(`
      SELECT
        subject_name,
        COUNT(*) as total_topics,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as completion_percentage
      FROM teacher_lectures
      GROUP BY subject_name
      ORDER BY completion_percentage DESC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/academic-progress', {
      title: 'Academic Intelligence',
      layout: 'layouts/principal',
      currentPage: 'academic-progress',
      classProgress,
      subjectProgress
    });
  } catch (error) {
    console.error('Error loading academic progress:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load academic progress',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Teacher Management
exports.getTeacherManagement = async (req, res) => {
  try {
    // Get teacher performance data
    const [teachers] = await db.query(`
      SELECT
        u.id,
        u.name,
        u.email,
        u.full_name,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate,
        u.last_login
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email, u.full_name, u.last_login
      ORDER BY completion_rate DESC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/teacher-management', {
      title: 'Faculty Operations',
      layout: 'layouts/principal',
      currentPage: 'teacher-management',
      teachers
    });
  } catch (error) {
    console.error('Error loading teacher management:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher management',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Teacher Timetables
exports.getTeacherTimetables = async (req, res) => {
  try {
    // Get teacher timetable data
    const [timetables] = await db.query(`
      SELECT
        u.name as teacher_name,
        tl.date,
        tl.start_time,
        tl.end_time,
        tl.subject_name,
        tl.class_name,
        tl.topic,
        tl.status
      FROM teacher_lectures tl
      JOIN users u ON tl.teacher_id = u.id
      WHERE tl.date >= CURDATE()
      ORDER BY tl.date ASC, tl.start_time ASC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/teacher-timetables', {
      title: 'Strategic Calendar',
      layout: 'layouts/principal',
      currentPage: 'teacher-timetables',
      timetables
    });
  } catch (error) {
    console.error('Error loading teacher timetables:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher timetables',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Student Analytics
exports.getStudentAnalytics = async (req, res) => {
  try {
    // Get student statistics
    const [studentStats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1 AND last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as active_students,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 0) as inactive_students
    `);

    res.render('principal/student-analytics', {
      title: 'Student Analytics',
      layout: 'layouts/principal',
      currentPage: 'student-analytics',
      studentStats: studentStats[0]
    });
  } catch (error) {
    console.error('Error loading student analytics:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load student analytics',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Get timetable data for principal view (API endpoint)
exports.getTimetableData = async (req, res) => {
  try {
    const { date } = req.query;
    const today = date || new Date().toISOString().split('T')[0];

    console.log('📊 Fetching comprehensive timetable data for date:', today);

    // Get day of week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = new Date(today).getDay();
    const currentTime = new Date();
    const currentTimeString = currentTime.toTimeString().slice(0, 8);

    // Get lectures from teacher_lectures table with enhanced data
    const [lectures] = await db.query(`
      SELECT
        tl.id,
        tl.teacher_id,
        tl.date,
        tl.start_time,
        tl.end_time,
        tl.slot_index,
        tl.class_name,
        tl.section_display,
        tl.subject_name,
        tl.topic,
        tl.grade,
        tl.stream,
        tl.location,
        tl.status,
        u.name as teacher_name,
        u.full_name as teacher_full_name,
        u.email as teacher_email,
        s.code as subject_code,
        s.id as subject_id
      FROM teacher_lectures tl
      LEFT JOIN users u ON tl.teacher_id = u.id
      LEFT JOIN subjects s ON s.name = tl.subject_name
      WHERE tl.date = ?
      ORDER BY tl.start_time ASC, tl.class_name ASC
    `, [today]);

    // Get from class_weekly_lectures for recurring schedule
    let weeklyLectures = [];
    try {
      const [weeklyResult] = await db.query(`
        SELECT
          cwl.id,
          cwl.day_of_week,
          cwl.period,
          cwl.start_time,
          cwl.end_time,
          cwl.room as location,
          cwl.teacher_id,
          s.name as subject_name,
          s.code as subject_code,
          s.id as subject_id,
          u.name as teacher_name,
          u.full_name as teacher_full_name,
          u.email as teacher_email,
          c.name as class_name,
          c.grade,
          c.section,
          CONCAT(c.grade, ' ', c.section) as class_display
        FROM class_weekly_lectures cwl
        LEFT JOIN subjects s ON cwl.subject_id = s.id
        LEFT JOIN users u ON cwl.teacher_id = u.id
        LEFT JOIN classes c ON cwl.class_id = c.id
        WHERE cwl.day_of_week = ?
        ORDER BY cwl.period ASC, c.name ASC
      `, [dayOfWeek]);

      weeklyLectures = weeklyResult;
    } catch (error) {
      console.log('Weekly lectures table may not exist:', error.message);
    }

    // Get from lecture_schedule table for additional data
    let scheduledLectures = [];
    try {
      const [scheduleResult] = await db.query(`
        SELECT
          ls.id,
          ls.day_of_week,
          ls.start_time,
          ls.end_time,
          ls.classroom as location,
          ls.teacher_id,
          s.name as subject_name,
          s.code as subject_code,
          s.id as subject_id,
          u.name as teacher_name,
          u.full_name as teacher_full_name,
          u.email as teacher_email,
          c.name as class_name,
          c.grade,
          c.section,
          CASE ls.day_of_week
            WHEN 'Monday' THEN 1
            WHEN 'Tuesday' THEN 2
            WHEN 'Wednesday' THEN 3
            WHEN 'Thursday' THEN 4
            WHEN 'Friday' THEN 5
            WHEN 'Saturday' THEN 6
            WHEN 'Sunday' THEN 0
          END as day_number
        FROM lecture_schedule ls
        JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
        JOIN subjects s ON sca.subject_id = s.id
        JOIN classes c ON sca.class_id = c.id
        JOIN users u ON ls.teacher_id = u.id
        WHERE CASE ls.day_of_week
          WHEN 'Monday' THEN 1
          WHEN 'Tuesday' THEN 2
          WHEN 'Wednesday' THEN 3
          WHEN 'Thursday' THEN 4
          WHEN 'Friday' THEN 5
          WHEN 'Saturday' THEN 6
          WHEN 'Sunday' THEN 0
        END = ?
        AND ls.is_active = 1
        ORDER BY ls.start_time ASC, c.name ASC
      `, [dayOfWeek]);

      scheduledLectures = scheduleResult;
    } catch (error) {
      console.log('Lecture schedule table may not exist:', error.message);
    }

    // Combine and format the data with priority: daily lectures > weekly lectures > scheduled lectures
    const allLectures = [...lectures];

    // Add weekly lectures if no daily lectures found for specific time slots
    if (weeklyLectures.length > 0) {
      weeklyLectures.forEach(wl => {
        // Check if there's already a daily lecture for this time slot and class
        const existingLecture = lectures.find(l =>
          l.start_time === wl.start_time &&
          l.class_name === wl.class_name
        );

        if (!existingLecture) {
          allLectures.push({
            id: `weekly_${wl.id}`,
            teacher_id: wl.teacher_id,
            date: today,
            start_time: wl.start_time,
            end_time: wl.end_time,
            slot_index: wl.period - 1, // Convert to 0-based index
            class_name: wl.class_display || wl.class_name,
            section_display: wl.section,
            subject_name: wl.subject_name,
            subject_code: wl.subject_code,
            subject_id: wl.subject_id,
            topic: null,
            grade: wl.grade,
            stream: null,
            location: wl.location || 'Classroom',
            status: 'scheduled',
            teacher_name: wl.teacher_name,
            teacher_full_name: wl.teacher_full_name,
            teacher_email: wl.teacher_email,
            source: 'weekly'
          });
        }
      });
    }

    // Add scheduled lectures if no other lectures found
    if (scheduledLectures.length > 0) {
      scheduledLectures.forEach(sl => {
        // Check if there's already a lecture for this time slot and class
        const existingLecture = allLectures.find(l =>
          l.start_time === sl.start_time &&
          l.class_name === sl.class_name
        );

        if (!existingLecture) {
          allLectures.push({
            id: `scheduled_${sl.id}`,
            teacher_id: sl.teacher_id,
            date: today,
            start_time: sl.start_time,
            end_time: sl.end_time,
            slot_index: null, // Will be calculated based on time
            class_name: `${sl.grade} ${sl.section}`,
            section_display: sl.section,
            subject_name: sl.subject_name,
            subject_code: sl.subject_code,
            subject_id: sl.subject_id,
            topic: null,
            grade: sl.grade,
            stream: null,
            location: sl.location || 'Classroom',
            status: 'scheduled',
            teacher_name: sl.teacher_name,
            teacher_full_name: sl.teacher_full_name,
            teacher_email: sl.teacher_email,
            source: 'schedule'
          });
        }
      });
    }

    // Calculate slot_index for lectures that don't have it
    const timeSlots = [
      { start: '08:00:00', end: '08:40:00', period: 1 },
      { start: '08:45:00', end: '09:25:00', period: 2 },
      { start: '09:30:00', end: '10:10:00', period: 3 },
      { start: '10:15:00', end: '10:55:00', period: 4 },
      { start: '11:00:00', end: '11:40:00', period: 5 },
      { start: '11:45:00', end: '12:25:00', period: 6 },
      { start: '12:30:00', end: '13:10:00', period: 7 },
      { start: '13:15:00', end: '13:55:00', period: 8 }
    ];

    allLectures.forEach(lecture => {
      if (lecture.slot_index === null || lecture.slot_index === undefined) {
        const matchingSlot = timeSlots.find(slot => slot.start === lecture.start_time);
        lecture.slot_index = matchingSlot ? matchingSlot.period : 0;
      }
    });

    // Add current status based on time and database status
    allLectures.forEach(lecture => {
      const lectureStart = new Date(`${today}T${lecture.start_time}`);
      const lectureEnd = new Date(`${today}T${lecture.end_time}`);
      const now = new Date();

      // Check database status first
      if (lecture.status === 'delivered' || lecture.status === 'completed') {
        lecture.currentStatus = 'completed';
      } else if (lecture.status === 'cancelled') {
        lecture.currentStatus = 'cancelled';
      } else if (now >= lectureStart && now <= lectureEnd) {
        lecture.currentStatus = 'active';
      } else if (now > lectureEnd) {
        lecture.currentStatus = 'completed';
      } else {
        lecture.currentStatus = 'upcoming';
      }
    });

    console.log(`✅ Found ${allLectures.length} lectures for ${today} (${lectures.length} daily, ${weeklyLectures.length} weekly, ${scheduledLectures.length} scheduled)`);

    res.json({
      success: true,
      date: today,
      dayOfWeek,
      currentTime: currentTimeString,
      lectures: allLectures,
      timeSlots: timeSlots,
      stats: {
        totalLectures: allLectures.length,
        dailyLectures: lectures.length,
        weeklyLectures: weeklyLectures.length,
        scheduledLectures: scheduledLectures.length,
        activeLectures: allLectures.filter(l => l.currentStatus === 'active').length,
        completedLectures: allLectures.filter(l => l.currentStatus === 'completed').length,
        upcomingLectures: allLectures.filter(l => l.currentStatus === 'upcoming').length
      }
    });

  } catch (error) {
    console.error('❌ Error fetching timetable data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching timetable data',
      error: error.message
    });
  }
};

// Get individual teacher details (API endpoint for principal view)
exports.getTeacherDetails = async (req, res) => {
  try {
    const teacherId = req.params.id;

    // Get teacher basic information
    const [teacherResult] = await db.query(`
      SELECT
        id, username, name, email, profile_image, subjects, bio,
        date_of_birth, created_at, last_login, is_active
      FROM users
      WHERE id = ? AND role = 'teacher'
    `, [teacherId]);

    if (teacherResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherResult[0];

    // Get additional statistics
    try {
      // Get lecture count
      const [lectureCount] = await db.query(`
        SELECT COUNT(*) as count
        FROM teacher_lectures
        WHERE teacher_id = ?
      `, [teacherId]);

      // Get practical count
      const [practicalCount] = await db.query(`
        SELECT COUNT(*) as count
        FROM teacher_practicals
        WHERE teacher_id = ?
      `, [teacherId]);

      // Get student count (approximate based on class assignments)
      const [studentCount] = await db.query(`
        SELECT COUNT(DISTINCT s.id) as count
        FROM students s
        JOIN teacher_subjects ts ON ts.teacher_id = ?
        WHERE s.class = ts.class_name OR s.class IS NOT NULL
      `, [teacherId]);

      // Add statistics to teacher object
      teacher.lecture_count = lectureCount[0]?.count || 0;
      teacher.practical_count = practicalCount[0]?.count || 0;
      teacher.student_count = studentCount[0]?.count || 0;

    } catch (statsError) {
      console.error('Error fetching teacher statistics:', statsError);
      // Set default values if statistics query fails
      teacher.lecture_count = 0;
      teacher.practical_count = 0;
      teacher.student_count = 0;
    }

    res.json({
      success: true,
      teacher
    });

  } catch (error) {
    console.error('Error fetching teacher details:', error);
    res.status(500).json({
      success: false,
      message: 'Error loading teacher details'
    });
  }
};

// Principal Profile
exports.getProfile = async (req, res) => {
  try {
    // Get basic principal information from users table only (to avoid column issues)
    const [users] = await db.query(`
      SELECT
        u.id, u.username, u.name, u.email, u.profile_image, u.bio,
        u.date_of_birth, u.created_at, u.last_login, u.is_active
      FROM users u
      WHERE u.id = ? AND u.role = 'principal'
    `, [req.session.userId]);

    // Try to get staff information separately with error handling
    let staffInfo = null;
    if (users.length > 0) {
      try {
        const [staffResult] = await db.query(`
          SELECT
            s.id as staff_id, s.employee_id, s.designation, s.department,
            s.joining_date, s.employment_type, s.phone, s.alternate_phone,
            s.emergency_contact, s.address, s.city, s.state, s.pincode,
            s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
            s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
            s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
            s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
            s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
            s.other_qualifications, s.professional_certifications,
            s.total_experience_years, s.teaching_experience_years, s.administrative_experience_years,
            s.previous_organizations, s.current_salary, s.subjects_taught, s.classes_handled,
            s.awards_received, s.publications, s.research_papers, s.conferences_attended, s.training_programs,
            s.special_skills, s.languages_known, s.office_location, s.performance_rating
          FROM staff s
          WHERE s.user_id = ?
        `, [req.session.userId]);

        if (staffResult.length > 0) {
          staffInfo = staffResult[0];
        }
      } catch (staffError) {
        console.log('Staff table query failed, using defaults:', staffError.message);
        staffInfo = {
          staff_id: null,
          employee_id: `EMP${String(req.session.userId).padStart(4, '0')}`,
          designation: 'Principal',
          department: 'Administration',
          joining_date: null,
          employment_type: 'permanent',
          phone: 'Not provided',
          alternate_phone: null,
          emergency_contact: null,
          address: null,
          city: null,
          state: null,
          pincode: null
        };
      }
    }

    if (users.length === 0) {
      return res.status(404).render('error', {
        title: 'User Not Found',
        message: 'Principal profile not found',
        error: { status: 404 },
        layout: 'layouts/principal'
      });
    }

    const user = users[0];

    // Combine user and staff information
    const combinedUser = {
      ...user,
      ...staffInfo
    };

    // Get educational qualifications from staff table data
    let educationTimeline = [];
    if (staffInfo) {
      // Build education timeline from staff table columns
      if (staffInfo.class_10_board) {
        educationTimeline.push({
          qualification_level: '10th',
          qualification_name: 'Class 10 (Secondary)',
          institution_name: staffInfo.class_10_school || 'Not specified',
          university_board: staffInfo.class_10_board,
          completion_year: staffInfo.class_10_year,
          percentage: staffInfo.class_10_percentage,
          specialization: null
        });
      }

      if (staffInfo.class_12_board) {
        educationTimeline.push({
          qualification_level: '12th',
          qualification_name: 'Class 12 (Higher Secondary)',
          institution_name: staffInfo.class_12_school || 'Not specified',
          university_board: staffInfo.class_12_board,
          completion_year: staffInfo.class_12_year,
          percentage: staffInfo.class_12_percentage,
          specialization: staffInfo.class_12_stream
        });
      }

      if (staffInfo.graduation_degree) {
        educationTimeline.push({
          qualification_level: 'graduation',
          qualification_name: staffInfo.graduation_degree,
          institution_name: staffInfo.graduation_university || 'Not specified',
          university_board: staffInfo.graduation_university,
          completion_year: staffInfo.graduation_year,
          percentage: staffInfo.graduation_percentage,
          specialization: staffInfo.graduation_specialization
        });
      }

      if (staffInfo.post_graduation_degree) {
        educationTimeline.push({
          qualification_level: 'post_graduation',
          qualification_name: staffInfo.post_graduation_degree,
          institution_name: staffInfo.post_graduation_university || 'Not specified',
          university_board: staffInfo.post_graduation_university,
          completion_year: staffInfo.post_graduation_year,
          percentage: staffInfo.post_graduation_percentage,
          specialization: staffInfo.post_graduation_specialization
        });
      }

      if (staffInfo.phd_subject) {
        educationTimeline.push({
          qualification_level: 'phd',
          qualification_name: 'Doctor of Philosophy (PhD)',
          institution_name: staffInfo.phd_university || 'Not specified',
          university_board: staffInfo.phd_university,
          completion_year: staffInfo.phd_year,
          percentage: null,
          specialization: staffInfo.phd_subject,
          thesis_title: staffInfo.phd_thesis_title
        });
      }

      // Sort by completion year
      if (educationTimeline && educationTimeline.length > 0) {
        educationTimeline.sort((a, b) => (a.completion_year || 0) - (b.completion_year || 0));
      }
    }

    // Get professional experience if staff_id exists (with error handling)
    let experienceTimeline = [];
    if (staffInfo && staffInfo.staff_id) {
      try {
        const [experienceResult] = await db.query(`
          SELECT
            pe.job_title,
            pe.department,
            pe.employment_type,
            pe.job_category,
            pe.organization_name,
            pe.organization_type,
            pe.location,
            pe.start_date,
            pe.end_date,
            pe.is_current,
            pe.job_description,
            pe.key_achievements,
            pe.salary_range,
            pe.reporting_to,
            pe.team_size_managed
          FROM staff_professional_experience pe
          WHERE pe.staff_id = ?
          ORDER BY pe.start_date DESC
        `, [staffInfo.staff_id]);
        experienceTimeline = experienceResult;
      } catch (expError) {
        console.log('Professional experience table not found, using fallback data');
        experienceTimeline = [];
      }
    }

    // Get school information
    let schoolInfo = null;
    try {
      console.log('Fetching school information...');
      const [schoolResult] = await db.query(`
        SELECT * FROM school_information WHERE is_active = 1 LIMIT 1
      `);
      console.log('School query result:', schoolResult);
      if (schoolResult.length > 0) {
        schoolInfo = schoolResult[0];
        console.log('School info set:', schoolInfo);
      }

      // Always calculate actual school capacity from all classrooms
      console.log('Calculating actual school capacity from classrooms...');
      const [capacityResult] = await db.query(`
        SELECT
          COUNT(r.id) as total_classrooms,
          SUM(r.capacity) as total_student_capacity,
          AVG(r.capacity) as average_classroom_capacity
        FROM rooms r
        WHERE r.room_number LIKE 'Room %'
        AND r.capacity > 0
      `);

      if (capacityResult && capacityResult.length > 0) {
        const capacityData = capacityResult[0];
        console.log('Capacity calculation result:', capacityData);

        // If we have school info, update it; otherwise create it
        if (schoolInfo) {
          // Update existing school info with calculated capacity
          schoolInfo.calculated_total_classrooms = capacityData.total_classrooms || 0;
          schoolInfo.calculated_student_capacity = capacityData.total_student_capacity || 0;
          schoolInfo.calculated_average_capacity = Math.round(capacityData.average_classroom_capacity || 0);

          // Override the static values with calculated ones
          schoolInfo.total_classrooms = schoolInfo.calculated_total_classrooms;
          schoolInfo.total_students_capacity = schoolInfo.calculated_student_capacity;

          console.log('Updated school info with calculated capacity:', {
            total_classrooms: schoolInfo.total_classrooms,
            total_students_capacity: schoolInfo.total_students_capacity,
            average_capacity: schoolInfo.calculated_average_capacity
          });
        } else {
          // Create school info with calculated capacity
          schoolInfo = {
            school_name: 'Government Senior Secondary School, Model Town',
            establishment_year: 2014,
            school_type: 'government',
            total_students_capacity: capacityData.total_student_capacity || 1000,
            total_classrooms: capacityData.total_classrooms || 20,
            total_teaching_staff_capacity: 45
          };
          console.log('Created school info with calculated capacity:', schoolInfo);
        }
      }
    } catch (schoolError) {
      console.log('School information error:', schoolError);

      // Calculate capacity even for fallback data
      let calculatedCapacity = 1200; // default fallback
      let calculatedClassrooms = 24; // default fallback

      try {
        const [capacityResult] = await db.query(`
          SELECT
            COUNT(r.id) as total_classrooms,
            SUM(r.capacity) as total_student_capacity
          FROM rooms r
          WHERE r.room_number LIKE 'Room %'
          AND r.capacity > 0
        `);

        if (capacityResult && capacityResult.length > 0) {
          calculatedCapacity = capacityResult[0].total_student_capacity || 1200;
          calculatedClassrooms = capacityResult[0].total_classrooms || 24;
          console.log('Calculated capacity for fallback:', { calculatedCapacity, calculatedClassrooms });
        }
      } catch (capacityError) {
        console.log('Capacity calculation failed, using defaults:', capacityError);
      }

      schoolInfo = {
        school_name: 'Government Senior Secondary School, Model Town',
        establishment_year: 2014,
        school_type: 'government',
        total_students_capacity: calculatedCapacity,
        total_classrooms: calculatedClassrooms,
        total_teaching_staff_capacity: 45
      };
      console.log('School info fallback set:', schoolInfo);
    }

    // Get principal timeline (leadership history)
    let principalTimeline = [];
    try {
      const [timelineResult] = await db.query(`
        SELECT
          principal_name, start_date, end_date, tenure_duration_months,
          is_current, appointment_type, major_achievements,
          infrastructure_developments, academic_improvements,
          awards_during_tenure, previous_school, experience_years
        FROM principal_timeline
        ORDER BY start_date DESC
      `);
      principalTimeline = timelineResult;
    } catch (timelineError) {
      console.log('Principal timeline table not found, using defaults');
      principalTimeline = [];
    }

    // Get recent activities (real data)
    const [recentActivities] = await db.query(`
      SELECT
        'Teacher Login' as activity_type,
        u.name as activity_description,
        u.last_login as activity_date
      FROM users u
      WHERE u.role = 'teacher' AND u.last_login IS NOT NULL
      ORDER BY u.last_login DESC
      LIMIT 5
    `);

    console.log('Debug - schoolInfo:', schoolInfo);
    console.log('Debug - principalTimeline:', principalTimeline);
    console.log('Debug - educationTimeline:', educationTimeline);

    res.render('principal/profile', {
      title: 'Principal Profile',
      layout: 'layouts/principal',
      currentPage: 'profile',
      user: combinedUser,
      educationTimeline,
      experienceTimeline,
      recentActivities,
      schoolInfo,
      principalTimeline,
      formatDate: (date) => {
        if (!date) return 'N/A';
        return new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      },
      formatDateTime: (date) => {
        if (!date) return 'N/A';
        return new Date(date).toLocaleString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }
    });
  } catch (error) {
    console.error('Error loading principal profile:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load principal profile',
      error: { status: 500 },
      layout: 'layouts/principal'
    });
  }
};

// Infrastructure Overview
exports.getInfrastructure = async (req, res) => {
  try {
    // First, update all room capacities to 50 if they're not already
    await db.query(`
      UPDATE rooms SET capacity = 50 WHERE capacity != 50
    `);

    // Get actual classroom data with room information including capacity
    const [classroomData] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.floor,
        r.capacity,
        c.id as classroom_id,
        c.session,
        cl.grade,
        cl.section,
        t.name as trade_name,
        u.name as incharge_name,
        COUNT(sc.student_id) as student_count,
        ROUND((COUNT(sc.student_id) / r.capacity) * 100, 1) as utilization_percentage
      FROM rooms r
      LEFT JOIN classrooms c ON r.id = c.room_id AND c.is_active = 1
      LEFT JOIN classes cl ON c.class_id = cl.id
      LEFT JOIN trades t ON c.trade_id = t.id
      LEFT JOIN users u ON c.incharge = u.id
      LEFT JOIN student_classrooms sc ON c.id = sc.classroom_id AND sc.status = 'active'
      WHERE r.room_number LIKE 'Room %'
      GROUP BY r.id, r.room_number, r.floor, r.capacity, c.id, c.session, cl.grade, cl.section, t.name, u.name
      ORDER BY r.id
    `);

    // Get Computer Labs with equipment details
    const [computerLabs] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%EZVIZ%' OR i.name LIKE '%Camera%' OR i.model LIKE '%Camera%' THEN i.item_id END) as cameras,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE 'Computer Lab%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Science Labs with equipment details
    const [scienceLabs] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number IN ('Biology Lab', 'Chemistry Lab', 'Physics Lab')
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Library with equipment details
    const [library] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number = 'Library'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
    `);

    // Get Offices with equipment details
    const [offices] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%iPad%' OR i.name LIKE '%Tablet%' THEN i.item_id END) as tablets
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE '%Office%' AND r.room_number NOT LIKE '%Hostel%' AND r.room_number NOT LIKE '%Computer Office%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Hostel Warden Rooms with equipment details
    const [hostelRooms] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' OR i.name LIKE '%TV%' THEN i.item_id END) as tvs,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE '%Hostel Warden Room%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get electrical inventory summary
    const [electricalSummary] = await db.query(`
      SELECT
        COUNT(*) as total_items,
        SUM(CASE WHEN status = 'working' THEN 1 ELSE 0 END) as working_items,
        SUM(CASE WHEN status = 'faulty' THEN 1 ELSE 0 END) as faulty_items
      FROM electrical_inventory
    `);

    res.render('principal/infrastructure', {
      title: 'Infrastructure Command Center',
      layout: 'layouts/principal',
      currentPage: 'infrastructure',
      classroomData,
      computerLabs,
      scienceLabs,
      library: library[0] || null,
      offices,
      hostelRooms,
      electricalSummary: electricalSummary[0] || { total_items: 0, working_items: 0, faulty_items: 0 }
    });
  } catch (error) {
    console.error('Error loading infrastructure:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load infrastructure overview',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Reports
exports.getReports = async (req, res) => {
  try {
    res.render('principal/reports', {
      title: 'Reports & Analytics',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Academic Reports
exports.getAcademicReports = async (req, res) => {
  try {
    res.render('principal/reports/academic', {
      title: 'Academic Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading academic reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load academic reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Attendance Reports
exports.getAttendanceReports = async (req, res) => {
  try {
    res.render('principal/reports/attendance', {
      title: 'Attendance Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading attendance reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load attendance reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Performance Reports
exports.getPerformanceReports = async (req, res) => {
  try {
    res.render('principal/reports/performance', {
      title: 'Performance Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading performance reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load performance reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};
