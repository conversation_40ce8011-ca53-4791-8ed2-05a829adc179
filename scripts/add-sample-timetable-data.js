/**
 * <PERSON><PERSON><PERSON> to add sample timetable data for testing the principal timetable view
 */

const db = require('../config/database');

async function addSampleTimetableData() {
  try {
    console.log('🎯 Adding sample timetable data...');
    
    // Get today's date
    const today = new Date().toISOString().split('T')[0];
    
    // Check if we have any teachers
    const [teachers] = await db.query(`
      SELECT id, name, full_name FROM users WHERE role = 'teacher' LIMIT 5
    `);
    
    if (teachers.length === 0) {
      console.log('❌ No teachers found. Please add teachers first.');
      return;
    }
    
    console.log(`✅ Found ${teachers.length} teachers`);
    
    // Check if teacher_lectures table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_lectures'
    `);
    
    if (tableExists[0].table_exists === 0) {
      console.log('❌ teacher_lectures table does not exist. Creating it...');
      
      await db.query(`
        CREATE TABLE teacher_lectures (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          slot_index INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          section_display VARCHAR(10),
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255),
          grade VARCHAR(10),
          streamCode VARCHAR(10),
          sectionLetter VARCHAR(5),
          stream VARCHAR(50),
          location VARCHAR(100),
          status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          class_section_id INT,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ teacher_lectures table created');
    }
    
    // Clear existing data for today
    await db.query(`DELETE FROM teacher_lectures WHERE date = ?`, [today]);
    console.log('🧹 Cleared existing data for today');
    
    // Time slots
    const timeSlots = [
      { start: '08:00:00', end: '08:40:00', slot: 0 },
      { start: '08:50:00', end: '09:30:00', slot: 1 },
      { start: '09:40:00', end: '10:20:00', slot: 2 },
      { start: '10:30:00', end: '11:10:00', slot: 3 },
      { start: '11:20:00', end: '12:00:00', slot: 4 },
      { start: '12:10:00', end: '12:50:00', slot: 5 },
      { start: '13:00:00', end: '13:40:00', slot: 6 },
      { start: '13:50:00', end: '14:30:00', slot: 7 }
    ];
    
    // Sample classes
    const classes = [
      { name: '11 Non-Medical A', grade: '11', stream: 'Non-Medical', section: 'A' },
      { name: '11 Non-Medical B', grade: '11', stream: 'Non-Medical', section: 'B' },
      { name: '12 Non-Medical A', grade: '12', stream: 'Non-Medical', section: 'A' },
      { name: '11 Medical A', grade: '11', stream: 'Medical', section: 'A' },
      { name: '12 Commerce A', grade: '12', stream: 'Commerce', section: 'A' }
    ];
    
    // Sample subjects
    const subjects = [
      'Mathematics', 'Physics', 'Chemistry', 'Computer Science', 
      'English', 'Biology', 'Accountancy', 'Business Studies'
    ];
    
    // Sample locations
    const locations = [
      'Classroom 1', 'Classroom 2', 'Physics Lab', 'Chemistry Lab',
      'Computer Lab 1', 'Computer Lab 2', 'Biology Lab', 'Library'
    ];
    
    // Generate sample lectures
    let lectureCount = 0;
    
    for (let classIndex = 0; classIndex < classes.length; classIndex++) {
      const classInfo = classes[classIndex];
      
      for (let slotIndex = 0; slotIndex < 6; slotIndex++) { // 6 periods per class
        const timeSlot = timeSlots[slotIndex];
        const teacher = teachers[lectureCount % teachers.length];
        const subject = subjects[lectureCount % subjects.length];
        const location = locations[lectureCount % locations.length];
        
        // Determine status based on time
        const now = new Date();
        const lectureTime = new Date(`${today}T${timeSlot.start}`);
        const lectureEndTime = new Date(`${today}T${timeSlot.end}`);

        let status = 'pending';
        if (now > lectureEndTime) {
          status = 'delivered'; // Use 'delivered' instead of 'completed'
        } else if (now >= lectureTime && now <= lectureEndTime) {
          status = 'pending'; // Currently active
        }
        
        await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacher.id,
          today,
          timeSlot.start,
          timeSlot.end,
          timeSlot.slot,
          classInfo.name,
          classInfo.section,
          subject,
          `${subject} - Topic ${slotIndex + 1}`,
          classInfo.grade,
          classInfo.stream,
          location,
          status
        ]);
        
        lectureCount++;
      }
    }
    
    console.log(`✅ Added ${lectureCount} sample lectures for today (${today})`);
    
    // Show summary
    const [summary] = await db.query(`
      SELECT 
        COUNT(*) as total_lectures,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
      FROM teacher_lectures 
      WHERE date = ?
    `, [today]);
    
    console.log('📊 Summary:', summary[0]);
    
    // Show sample data
    const [sampleData] = await db.query(`
      SELECT 
        tl.start_time, tl.end_time, tl.class_name, tl.subject_name,
        u.name as teacher_name, tl.location, tl.status
      FROM teacher_lectures tl
      LEFT JOIN users u ON tl.teacher_id = u.id
      WHERE tl.date = ?
      ORDER BY tl.start_time, tl.class_name
      LIMIT 10
    `, [today]);
    
    console.log('\n📋 Sample lectures:');
    console.table(sampleData);
    
    console.log('\n🎉 Sample timetable data added successfully!');
    console.log('You can now view the timetable in the Principal dashboard.');
    
  } catch (error) {
    console.error('❌ Error adding sample timetable data:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
addSampleTimetableData();
