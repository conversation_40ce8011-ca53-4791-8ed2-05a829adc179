const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'exam_prep_platform'
};

async function addSampleTimetableData() {
  let connection;
  
  try {
    console.log('🚀 Starting sample timetable data insertion...');
    
    // Create database connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected');
    
    // Get today's date
    const today = new Date().toISOString().split('T')[0];
    console.log('📅 Adding data for date:', today);
    
    // Clear existing data for today
    await connection.execute(`DELETE FROM teacher_lectures WHERE date = ?`, [today]);
    console.log('🧹 Cleared existing data for today');
    
    // Get some teachers
    const [teachers] = await connection.execute(`
      SELECT id, name FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 10
    `);
    
    if (teachers.length === 0) {
      console.log('❌ No teachers found. Please add teachers first.');
      return;
    }
    
    console.log(`👥 Found ${teachers.length} teachers`);
    
    // Time slots
    const timeSlots = [
      { start: '08:00:00', end: '08:40:00', slot: 0 },
      { start: '08:45:00', end: '09:25:00', slot: 1 },
      { start: '09:30:00', end: '10:10:00', slot: 2 },
      { start: '10:15:00', end: '10:55:00', slot: 3 },
      { start: '11:00:00', end: '11:40:00', slot: 4 },
      { start: '11:45:00', end: '12:25:00', slot: 5 },
      { start: '12:30:00', end: '13:10:00', slot: 6 },
      { start: '13:15:00', end: '13:55:00', slot: 7 }
    ];
    
    // Sample classes
    const classes = [
      { name: '11 Non-Medical A', grade: '11', stream: 'Non-Medical', section: 'A' },
      { name: '11 Non-Medical B', grade: '11', stream: 'Non-Medical', section: 'B' },
      { name: '12 Non-Medical A', grade: '12', stream: 'Non-Medical', section: 'A' },
      { name: '11 Medical A', grade: '11', stream: 'Medical', section: 'A' },
      { name: '12 Commerce A', grade: '12', stream: 'Commerce', section: 'A' }
    ];
    
    // Sample subjects
    const subjects = [
      'Mathematics', 'Physics', 'Chemistry', 'Computer Science', 
      'English', 'Biology', 'Accountancy', 'Business Studies', 'Economics'
    ];
    
    // Sample locations
    const locations = [
      'Classroom 1', 'Classroom 2', 'Physics Lab', 'Chemistry Lab',
      'Computer Lab 1', 'Computer Lab 2', 'Biology Lab', 'Library', 'Auditorium'
    ];
    
    // Sample topics
    const topics = {
      'Mathematics': ['Algebra', 'Calculus', 'Geometry', 'Trigonometry', 'Statistics'],
      'Physics': ['Mechanics', 'Thermodynamics', 'Optics', 'Electricity', 'Magnetism'],
      'Chemistry': ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Analytical Chemistry'],
      'Computer Science': ['Programming', 'Data Structures', 'Algorithms', 'Database', 'Networks'],
      'English': ['Grammar', 'Literature', 'Writing Skills', 'Poetry', 'Prose'],
      'Biology': ['Cell Biology', 'Genetics', 'Ecology', 'Human Physiology', 'Plant Biology'],
      'Accountancy': ['Financial Accounting', 'Cost Accounting', 'Auditing', 'Taxation'],
      'Business Studies': ['Management', 'Marketing', 'Finance', 'Human Resources'],
      'Economics': ['Microeconomics', 'Macroeconomics', 'International Trade', 'Development Economics']
    };
    
    let lectureCount = 0;
    
    // Generate lectures for each class and time slot
    for (const classInfo of classes) {
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        // Skip some slots randomly to create realistic gaps
        if (Math.random() < 0.2) continue; // 20% chance to skip
        
        const timeSlot = timeSlots[slotIndex];
        const teacher = teachers[Math.floor(Math.random() * teachers.length)];
        const subject = subjects[Math.floor(Math.random() * subjects.length)];
        const location = locations[Math.floor(Math.random() * locations.length)];
        const topicList = topics[subject] || ['General Topic'];
        const topic = topicList[Math.floor(Math.random() * topicList.length)];
        
        // Determine status based on current time
        const now = new Date();
        const lectureStart = new Date(`${today}T${timeSlot.start}`);
        const lectureEnd = new Date(`${today}T${timeSlot.end}`);

        let status = 'pending';
        if (now > lectureEnd) {
          status = Math.random() > 0.1 ? 'delivered' : 'cancelled'; // 90% delivered, 10% cancelled
        } else if (now >= lectureStart && now <= lectureEnd) {
          status = 'pending'; // Currently active
        }
        
        await connection.execute(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacher.id,
          today,
          timeSlot.start,
          timeSlot.end,
          timeSlot.slot,
          classInfo.name,
          classInfo.section,
          subject,
          topic,
          classInfo.grade,
          classInfo.stream,
          location,
          status
        ]);
        
        lectureCount++;
      }
    }
    
    console.log(`✅ Successfully added ${lectureCount} sample lectures for today`);
    
    // Show summary
    const [summary] = await connection.execute(`
      SELECT 
        status,
        COUNT(*) as count
      FROM teacher_lectures 
      WHERE date = ?
      GROUP BY status
    `, [today]);
    
    console.log('📊 Lecture Summary:');
    summary.forEach(row => {
      console.log(`   ${row.status}: ${row.count} lectures`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  addSampleTimetableData()
    .then(() => {
      console.log('🎉 Sample timetable data added successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = addSampleTimetableData;
