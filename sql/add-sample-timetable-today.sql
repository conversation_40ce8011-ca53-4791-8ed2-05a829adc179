-- Add sample timetable data for today's date
-- This will help test the enhanced timetable display functionality

-- Clear existing data for today
DELETE FROM teacher_lectures WHERE date = CURDATE();

-- Get some teacher IDs (assuming they exist)
SET @teacher1 = (SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1);
SET @teacher2 = (SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1 OFFSET 1);
SET @teacher3 = (SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1 OFFSET 2);
SET @teacher4 = (SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1 OFFSET 3);
SET @teacher5 = (SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1 OFFSET 4);

-- Insert sample lectures for today
INSERT INTO teacher_lectures (
  teacher_id, date, start_time, end_time, slot_index,
  class_name, section_display, subject_name, topic,
  grade, stream, location, status
) VALUES
-- Period 1 (08:00-08:40)
(@teacher1, CURDATE(), '08:00:00', '08:40:00', 0, '11 Non-Medical A', 'A', 'Mathematics', 'Calculus - Derivatives', '11', 'Non-Medical', 'Classroom 1', 'completed'),
(@teacher2, CURDATE(), '08:00:00', '08:40:00', 0, '11 Non-Medical B', 'B', 'Physics', 'Mechanics - Newton Laws', '11', 'Non-Medical', 'Physics Lab', 'completed'),
(@teacher3, CURDATE(), '08:00:00', '08:40:00', 0, '12 Non-Medical A', 'A', 'Chemistry', 'Organic Chemistry', '12', 'Non-Medical', 'Chemistry Lab', 'completed'),
(@teacher4, CURDATE(), '08:00:00', '08:40:00', 0, '11 Medical A', 'A', 'Biology', 'Cell Structure', '11', 'Medical', 'Biology Lab', 'completed'),
(@teacher5, CURDATE(), '08:00:00', '08:40:00', 0, '12 Commerce A', 'A', 'Accountancy', 'Financial Statements', '12', 'Commerce', 'Classroom 2', 'completed'),

-- Period 2 (08:45-09:25)
(@teacher2, CURDATE(), '08:45:00', '09:25:00', 1, '11 Non-Medical A', 'A', 'Physics', 'Thermodynamics', '11', 'Non-Medical', 'Physics Lab', 'completed'),
(@teacher1, CURDATE(), '08:45:00', '09:25:00', 1, '11 Non-Medical B', 'B', 'Mathematics', 'Algebra', '11', 'Non-Medical', 'Classroom 1', 'completed'),
(@teacher4, CURDATE(), '08:45:00', '09:25:00', 1, '12 Non-Medical A', 'A', 'Biology', 'Genetics', '12', 'Non-Medical', 'Biology Lab', 'completed'),
(@teacher3, CURDATE(), '08:45:00', '09:25:00', 1, '11 Medical A', 'A', 'Chemistry', 'Inorganic Chemistry', '11', 'Medical', 'Chemistry Lab', 'completed'),
(@teacher5, CURDATE(), '08:45:00', '09:25:00', 1, '12 Commerce A', 'A', 'Business Studies', 'Management Principles', '12', 'Commerce', 'Classroom 2', 'completed'),

-- Period 3 (09:30-10:10)
(@teacher3, CURDATE(), '09:30:00', '10:10:00', 2, '11 Non-Medical A', 'A', 'Chemistry', 'Chemical Bonding', '11', 'Non-Medical', 'Chemistry Lab', 'completed'),
(@teacher4, CURDATE(), '09:30:00', '10:10:00', 2, '11 Non-Medical B', 'B', 'Biology', 'Plant Physiology', '11', 'Non-Medical', 'Biology Lab', 'completed'),
(@teacher1, CURDATE(), '09:30:00', '10:10:00', 2, '12 Non-Medical A', 'A', 'Mathematics', 'Integration', '12', 'Non-Medical', 'Classroom 1', 'completed'),
(@teacher2, CURDATE(), '09:30:00', '10:10:00', 2, '11 Medical A', 'A', 'Physics', 'Optics', '11', 'Medical', 'Physics Lab', 'completed'),
(@teacher5, CURDATE(), '09:30:00', '10:10:00', 2, '12 Commerce A', 'A', 'Economics', 'Microeconomics', '12', 'Commerce', 'Classroom 2', 'completed'),

-- Period 4 (10:15-10:55) - Some current/active lectures
(@teacher1, CURDATE(), '10:15:00', '10:55:00', 3, '11 Non-Medical A', 'A', 'Mathematics', 'Trigonometry', '11', 'Non-Medical', 'Classroom 1', 'pending'),
(@teacher2, CURDATE(), '10:15:00', '10:55:00', 3, '11 Non-Medical B', 'B', 'Physics', 'Electricity', '11', 'Non-Medical', 'Physics Lab', 'pending'),
(@teacher3, CURDATE(), '10:15:00', '10:55:00', 3, '12 Non-Medical A', 'A', 'Chemistry', 'Physical Chemistry', '12', 'Non-Medical', 'Chemistry Lab', 'pending'),
(@teacher4, CURDATE(), '10:15:00', '10:55:00', 3, '11 Medical A', 'A', 'Biology', 'Human Physiology', '11', 'Medical', 'Biology Lab', 'pending'),
(@teacher5, CURDATE(), '10:15:00', '10:55:00', 3, '12 Commerce A', 'A', 'Accountancy', 'Cost Accounting', '12', 'Commerce', 'Classroom 2', 'pending'),

-- Period 5 (11:00-11:40) - Upcoming lectures
(@teacher2, CURDATE(), '11:00:00', '11:40:00', 4, '11 Non-Medical A', 'A', 'Physics', 'Magnetism', '11', 'Non-Medical', 'Physics Lab', 'pending'),
(@teacher3, CURDATE(), '11:00:00', '11:40:00', 4, '11 Non-Medical B', 'B', 'Chemistry', 'Analytical Chemistry', '11', 'Non-Medical', 'Chemistry Lab', 'pending'),
(@teacher4, CURDATE(), '11:00:00', '11:40:00', 4, '12 Non-Medical A', 'A', 'Biology', 'Ecology', '12', 'Non-Medical', 'Biology Lab', 'pending'),
(@teacher1, CURDATE(), '11:00:00', '11:40:00', 4, '11 Medical A', 'A', 'Mathematics', 'Statistics', '11', 'Medical', 'Classroom 1', 'pending'),
(@teacher5, CURDATE(), '11:00:00', '11:40:00', 4, '12 Commerce A', 'A', 'Business Studies', 'Marketing', '12', 'Commerce', 'Classroom 2', 'pending'),

-- Period 6 (11:45-12:25) - Upcoming lectures
(@teacher1, CURDATE(), '11:45:00', '12:25:00', 5, '11 Non-Medical A', 'A', 'Mathematics', 'Geometry', '11', 'Non-Medical', 'Classroom 1', 'pending'),
(@teacher4, CURDATE(), '11:45:00', '12:25:00', 5, '11 Non-Medical B', 'B', 'Biology', 'Molecular Biology', '11', 'Non-Medical', 'Biology Lab', 'pending'),
(@teacher2, CURDATE(), '11:45:00', '12:25:00', 5, '12 Non-Medical A', 'A', 'Physics', 'Modern Physics', '12', 'Non-Medical', 'Physics Lab', 'pending'),
(@teacher3, CURDATE(), '11:45:00', '12:25:00', 5, '11 Medical A', 'A', 'Chemistry', 'Biochemistry', '11', 'Medical', 'Chemistry Lab', 'pending'),
(@teacher5, CURDATE(), '11:45:00', '12:25:00', 5, '12 Commerce A', 'A', 'Economics', 'Macroeconomics', '12', 'Commerce', 'Classroom 2', 'pending'),

-- Period 7 (12:30-13:10) - Upcoming lectures
(@teacher3, CURDATE(), '12:30:00', '13:10:00', 6, '11 Non-Medical A', 'A', 'Chemistry', 'Environmental Chemistry', '11', 'Non-Medical', 'Chemistry Lab', 'pending'),
(@teacher1, CURDATE(), '12:30:00', '13:10:00', 6, '11 Non-Medical B', 'B', 'Mathematics', 'Probability', '11', 'Non-Medical', 'Classroom 1', 'pending'),
(@teacher4, CURDATE(), '12:30:00', '13:10:00', 6, '12 Non-Medical A', 'A', 'Biology', 'Biotechnology', '12', 'Non-Medical', 'Biology Lab', 'pending'),
(@teacher2, CURDATE(), '12:30:00', '13:10:00', 6, '11 Medical A', 'A', 'Physics', 'Nuclear Physics', '11', 'Medical', 'Physics Lab', 'pending'),
(@teacher5, CURDATE(), '12:30:00', '13:10:00', 6, '12 Commerce A', 'A', 'Accountancy', 'Auditing', '12', 'Commerce', 'Classroom 2', 'pending'),

-- Period 8 (13:15-13:55) - Upcoming lectures
(@teacher4, CURDATE(), '13:15:00', '13:55:00', 7, '11 Non-Medical A', 'A', 'Biology', 'Evolution', '11', 'Non-Medical', 'Biology Lab', 'pending'),
(@teacher2, CURDATE(), '13:15:00', '13:55:00', 7, '11 Non-Medical B', 'B', 'Physics', 'Waves', '11', 'Non-Medical', 'Physics Lab', 'pending'),
(@teacher1, CURDATE(), '13:15:00', '13:55:00', 7, '12 Non-Medical A', 'A', 'Mathematics', 'Differential Equations', '12', 'Non-Medical', 'Classroom 1', 'pending'),
(@teacher3, CURDATE(), '13:15:00', '13:55:00', 7, '11 Medical A', 'A', 'Chemistry', 'Medicinal Chemistry', '11', 'Medical', 'Chemistry Lab', 'pending'),
(@teacher5, CURDATE(), '13:15:00', '13:55:00', 7, '12 Commerce A', 'A', 'Business Studies', 'Finance', '12', 'Commerce', 'Classroom 2', 'pending');

-- Add some English and Computer Science lectures
INSERT INTO teacher_lectures (
  teacher_id, date, start_time, end_time, slot_index,
  class_name, section_display, subject_name, topic,
  grade, stream, location, status
) VALUES
-- Additional subjects for variety
(@teacher1, CURDATE(), '08:00:00', '08:40:00', 0, '11 Commerce A', 'A', 'English', 'Grammar and Composition', '11', 'Commerce', 'Classroom 3', 'completed'),
(@teacher2, CURDATE(), '09:30:00', '10:10:00', 2, '12 Non-Medical B', 'B', 'Computer Science', 'Data Structures', '12', 'Non-Medical', 'Computer Lab 1', 'completed'),
(@teacher3, CURDATE(), '11:00:00', '11:40:00', 4, '11 Commerce B', 'B', 'English', 'Literature', '11', 'Commerce', 'Library', 'pending'),
(@teacher4, CURDATE(), '12:30:00', '13:10:00', 6, '12 Commerce B', 'B', 'Computer Science', 'Programming', '12', 'Commerce', 'Computer Lab 2', 'pending');

-- Print completion message
SELECT 'Sample timetable data for today has been added successfully!' AS Message;

-- Show summary
SELECT 
  status,
  COUNT(*) as lecture_count
FROM teacher_lectures 
WHERE date = CURDATE()
GROUP BY status
ORDER BY status;
